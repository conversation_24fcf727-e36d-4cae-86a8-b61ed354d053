const { default: mongoose } = require("mongoose");
const { Profession } = require("../models/Profession");

exports.addProfession = async (req, res) => {
  try {
    const { professionTitle, subcategories, addedBy } = req.body;

    if (!professionTitle || !subcategories || !addedBy) {
      return res.status(400).json({ success: false, message: "Missing required fields" });
    }

    const adminId = new mongoose.Types.ObjectId(addedBy);

    const formattedSubcategories = subcategories.map((subcategory) => ({
      title: subcategory.title,
      addedBy: adminId,
      items: (subcategory.items || []).map((item) => ({
        name: item.name,
        addedBy: adminId,
      })),
    }));

    const newProfession = new Profession({
      professionTitle,
      addedBy: adminId,
      subcategories: formattedSubcategories,
    });

    const saved = await newProfession.save();

    res.status(201).json({
      success: true,
      message: "Profession added successfully",
      data: saved,
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({ success: false, message: "Server error", error: error.message });
  }
};

exports.getProfessions = async (req, res) => {
  try {
    const professions = await Profession.find({})
      .populate("addedBy", "name email") // Optional: if you want admin info
      .populate("subcategories.addedBy", "name email")
      .populate("subcategories.items.addedBy", "name email")
      .lean();

    res.status(200).json({
      success: true,
      message: "Professions fetched successfully",
      data: professions,
    });
  } catch (error) {
    console.error("Error fetching professions:", error);
    res.status(500).json({ success: false, message: "Server error", error: error.message });
  }
};
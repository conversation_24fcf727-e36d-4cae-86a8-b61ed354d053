import { Profession } from "../models/Profession";

export const addProfession = async (req, res) => {
  try {
    const { professionTitle, subcategories } = req.body;

    if (!professionTitle || !subcategories || !Array.isArray(subcategories)) {
      return res.status(400).json({ success: false, message: "Invalid input data" });
    }

    const profession = new Profession({
      professionTitle,
      subcategories: subcategories.map((subcategory) => ({
        title: subcategory.title,
        addedBy: req.user._id, // assuming admin ID is available via auth middleware
        items: (subcategory.items || []).map((item) => ({
          name: item.name,
          addedBy: req.user._id,
        })),
      })),
      addedBy: req.user._id,
    });

    const saved = await profession.save();

    res.status(201).json({
      success: true,
      message: "Profession added successfully",
      data: saved,
    });
  } catch (error) {
    res.status(500).json({ success: false, message: "Server error", error: error.message });
  }
};
const nodemailer = require("nodemailer");

const transporter = nodemailer.createTransport({
  host: process.env.EMAIL_HOST || "smtp-relay.brevo.com",
  port: parseInt(process.env.EMAIL_PORT) || 587,
  auth: {
    user: process.env.EMAIL_USER,
    pass: process.env.EMAIL_PASS,
  },
  secure: false, // true for 465, false for other ports
  tls: {
    rejectUnauthorized: false, // Allow self-signed certificates
  },
});

exports.sendEmail = async (to, subject, html) => {
  try {
    // Verify connection configuration
    await transporter.verify();

    const mailOptions = {
      from: process.env.SENDER_EMAIL || process.env.EMAIL_USER,
      to,
      subject,
      html,
    };

    const result = await transporter.sendMail(mailOptions);
    console.log("Email sent successfully:", result.messageId);
    return result;
  } catch (error) {
    console.error("Email sending failed:", error);
    throw new Error(`Failed to send email: ${error.message}`);
  }
};

import { cn } from "@/lib/utils";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useLocation, Link, useNavigate, useParams } from "react-router-dom";
import { useRegister } from "@/hooks/useRegister";
import { useLogin } from "@/hooks/useLogin";
import { useForgotPassword } from "@/hooks/useForgotPassword";
import { useResetPassword } from "@/hooks/useResetPassword";
import { useAuth } from "@/context/AuthContext";
import { Eye, EyeOff } from "lucide-react";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useMemo, useEffect, useState } from "react";
import { toast } from "sonner";

// ------------------ ZOD SCHEMAS ------------------ //
const registerSchema = z.object({
  username: z.string().min(3, "Username is too short"),
  email: z.string().email("Invalid email address"),
  password: z.string().min(6, "Password must be at least 6 characters"),
});

const loginSchema = z.object({
  email: z.string().email("Invalid email address"),
  password: z.string().min(1, "Password is required"),
});

const forgotSchema = z.object({
  email: z.string().email("Invalid email address"),
});

const resetSchema = z
  .object({
    password: z.string().min(6, "Password must be at least 6 characters"),
    confirmPassword: z.string(),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords do not match",
    path: ["confirmPassword"],
  });

// ------------------ PASSWORD INPUT COMPONENT ------------------ //
const PasswordInput = ({ id, placeholder, register, error, ...props }) => {
  const [showPassword, setShowPassword] = useState(false);

  return (
    <div className="relative">
      <Input
        id={id}
        type={showPassword ? "text" : "password"}
        {...register}
        placeholder={placeholder}
        className="pr-10"
        {...props}
      />
      <button
        type="button"
        onClick={() => setShowPassword(!showPassword)}
        className="absolute inset-y-0 right-0 flex items-center pr-3 text-muted-foreground hover:text-foreground transition-colors"
        aria-label={showPassword ? "Hide password" : "Show password"}
      >
        {showPassword ? (
          <EyeOff className="h-4 w-4" />
        ) : (
          <Eye className="h-4 w-4" />
        )}
      </button>
      {error && <p className="text-red-500 text-sm mt-1">{error.message}</p>}
    </div>
  );
};

// ------------------ MAIN COMPONENT ------------------ //
export function AuthForm({ className, ...props }) {
  const location = useLocation();
  const navigate = useNavigate();
  const params = useParams();
  const path = location.pathname;

  // Authentication hooks
  const { setUserData } = useAuth();
  const {
    handleRegister,
    loading: registerLoading,
    error: registerError,
  } = useRegister();
  const { handleLogin, loading: loginLoading, error: loginError } = useLogin();
  const {
    handleForgotPassword,
    loading: forgotLoading,
    error: forgotError,
  } = useForgotPassword();
  const {
    handleResetPassword,
    loading: resetLoading,
    error: resetError,
  } = useResetPassword();

  // Determine current loading state and error
  const loading =
    registerLoading || loginLoading || forgotLoading || resetLoading;
  const error = registerError || loginError || forgotError || resetError;

  const schema = useMemo(() => {
    switch (path) {
      case "/register":
        return registerSchema;
      case "/login":
        return loginSchema;
      case "/forgot-password":
        return forgotSchema;
      default:
        return path.startsWith("/reset-password")
          ? resetSchema
          : registerSchema;
    }
  }, [path]);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm({
    resolver: zodResolver(schema),
  });

  // Clear form and show error messages when error changes
  useEffect(() => {
    if (error) {
      toast.error(error);
    }
  }, [error]);

  // Extract reset parameters from URL for reset password
  const getResetParams = () => {
    if (path.startsWith("/reset-password")) {
      return {
        userId: params.userId,
        token: params.token,
      };
    }
    return { userId: null, token: null };
  };

  const onSubmit = async (data) => {
    try {
      switch (path) {
        case "/register":
          await handleRegister(data);
          toast.success("Registration successful! Please verify your email.");
          navigate("/email-verification-success", {
            state: { email: data.email },
          });
          break;

        case "/login":
          const loginResponse = await handleLogin(data);
          // Update AuthContext with user data
          if (loginResponse?.user) {
            setUserData(loginResponse.user);
          }
          toast.success("Login successful!");
          navigate("/dashboard"); // Redirect to dashboard or home
          break;

        case "/forgot-password":
          await handleForgotPassword(data);
          toast.success("Password reset email sent! Check your inbox.");
          reset();
          break;

        default:
          if (path.startsWith("/reset-password")) {
            const { userId, token } = getResetParams();
            if (!userId || !token) {
              toast.error(
                "Invalid reset link. Please request a new password reset."
              );
              return;
            }
            await handleResetPassword({
              userId,
              token,
              password: data.password,
            });
            toast.success("Password reset successful! You can now login.");
            reset(); // Clear form fields
            navigate("/login");
          }
          break;
      }
    } catch (err) {
      // Error handling is done in useEffect for error state
      console.error("Form submission error:", err);
    }
  };

  const renderFields = () => {
    switch (path) {
      case "/register":
        return (
          <>
            <div className="grid gap-3">
              <Label htmlFor="username">Username</Label>
              <Input
                id="username"
                {...register("username")}
                placeholder="Enter your username"
                autoComplete="username"
                aria-describedby={
                  errors.username ? "username-error" : undefined
                }
              />
              {errors.username && (
                <p
                  id="username-error"
                  className="text-red-500 text-sm"
                  role="alert"
                >
                  {errors.username.message}
                </p>
              )}
            </div>
            <div className="grid gap-3">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                {...register("email")}
                placeholder="Enter your email"
              />
              {errors.email && (
                <p className="text-red-500 text-sm">{errors.email.message}</p>
              )}
            </div>
            <div className="grid gap-3">
              <Label htmlFor="password">Password</Label>
              <PasswordInput
                id="password"
                placeholder="Enter your password"
                register={register("password")}
                error={errors.password}
              />
            </div>
          </>
        );

      case "/login":
        return (
          <>
            <div className="grid gap-3">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                {...register("email")}
                placeholder="Enter your email"
              />
              {errors.email && (
                <p className="text-red-500 text-sm">{errors.email.message}</p>
              )}
            </div>
            <div className="grid gap-3">
              <div className="flex items-center">
                <Label htmlFor="password">Password</Label>
                <Link
                  to="/forgot-password"
                  className="ml-auto text-sm underline-offset-4 hover:underline"
                >
                  Forgot your password?
                </Link>
              </div>
              <PasswordInput
                id="password"
                placeholder="Enter your password"
                register={register("password")}
                error={errors.password}
              />
            </div>
          </>
        );

      case "/forgot-password":
        return (
          <div className="grid gap-3">
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              type="email"
              {...register("email")}
              placeholder="Enter your email address"
            />
            {errors.email && (
              <p className="text-red-500 text-sm">{errors.email.message}</p>
            )}
          </div>
        );

      default:
        if (path.startsWith("/reset-password")) {
          return (
            <>
              <div className="grid gap-3">
                <Label htmlFor="password">New Password</Label>
                <PasswordInput
                  id="password"
                  placeholder="Enter your new password"
                  register={register("password")}
                  error={errors.password}
                />
              </div>
              <div className="grid gap-3">
                <Label htmlFor="confirmPassword">Confirm Password</Label>
                <PasswordInput
                  id="confirmPassword"
                  placeholder="Confirm your new password"
                  register={register("confirmPassword")}
                  error={errors.confirmPassword}
                />
              </div>
            </>
          );
        }
    }
  };

  const titleMap = {
    "/register": "Create an account",
    "/login": "Login to your account",
    "/forgot-password": "Forgot your password?",
  };

  return (
    <form
      onSubmit={handleSubmit(onSubmit)}
      className={cn("flex flex-col gap-6", className)}
      noValidate
      {...props}
    >
      <div className="flex flex-col items-center gap-2 text-center">
        <h1 className="text-2xl font-bold">
          {titleMap[path] || "Reset your password"}
        </h1>
        <p className="text-muted-foreground text-sm text-balance">
          {path === "/register"
            ? "Enter your details to register."
            : path === "/login"
            ? "Enter your credentials to continue."
            : path === "/forgot-password"
            ? "We'll send a reset link to your email."
            : path.startsWith("/reset-password")
            ? "Enter your new password below."
            : "Enter your credentials to continue."}
        </p>
      </div>

      <div className="grid gap-6">
        {renderFields()}

        <Button type="submit" className="w-full" disabled={loading}>
          {loading ? (
            <div className="flex items-center gap-2">
              <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
              Submitting...
            </div>
          ) : (
            <>
              {path === "/register"
                ? "Create Account"
                : path === "/forgot-password"
                ? "Send Reset Link"
                : path.startsWith("/reset-password")
                ? "Reset Password"
                : "Sign In"}
            </>
          )}
        </Button>
      </div>

      {path === "/login" && (
        <div className="text-center text-sm">
          Don&apos;t have an account?{" "}
          <Link to="/register" className="underline">
            Sign up
          </Link>
        </div>
      )}
      {path === "/register" && (
        <div className="text-center text-sm">
          Already have an account?{" "}
          <Link to="/login" className="underline">
            Login
          </Link>
        </div>
      )}
      {path === "/forgot-password" && (
        <div className="text-center text-sm">
          Remember your password?{" "}
          <Link to="/login" className="underline">
            Login
          </Link>
        </div>
      )}
    </form>
  );
}

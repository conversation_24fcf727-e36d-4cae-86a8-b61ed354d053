import { apiClient } from "@/config/api";
import { createContext, useContext, useEffect, useState } from "react";
import { useNavigate, useLocation } from "react-router-dom";

const AuthContext = createContext();

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null); // store user data
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();
  const location = useLocation();

  // Check token and fetch user on app load
  useEffect(() => {
    const fetchUser = async () => {
      // Skip fetching user if on verification pages
      const isVerificationPage =
        location.pathname.includes("/verify-email") ||
        location.pathname.includes("/email-verification-success");

      if (isVerificationPage) {
        setLoading(false);
        return;
      }

      // Only try to fetch user if we have a token
      const token = localStorage.getItem("token");
      if (!token) {
        setLoading(false);
        return;
      }

      try {
        const res = await apiClient.get("/auth/me"); // or your auth user route
        setUser(res.data.user);
      } catch (err) {
        setUser(null);
        // Clear invalid token
        localStorage.removeItem("token");
        localStorage.removeItem("user");
      } finally {
        setLoading(false);
      }
    };

    fetchUser();
  }, [location.pathname]);

  const login = async (formData) => {
    const res = await apiClient.post("/auth/login", formData);
    setUser(res.data.user);
    return res;
  };

  const setUserData = (userData) => {
    setUser(userData);
  };

  const register = async (formData) => {
    const res = await apiClient.post("/auth/register", formData);
    return res;
  };

  const logout = async () => {
    await apiClient.post("/auth/logout");
    setUser(null);
    navigate("/login");
  };

  return (
    <AuthContext.Provider
      value={{ user, login, register, logout, loading, setUserData }}
    >
      {children}
    </AuthContext.Provider>
  );
};

// Custom hook
export const useAuth = () => {
  return useContext(AuthContext);
};

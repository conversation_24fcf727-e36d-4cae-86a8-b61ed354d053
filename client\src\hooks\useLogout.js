import { toast } from "sonner";
import { useNavigate } from "react-router-dom";
import { API_ENDPOINTS } from "@/config/api";

export const useLogout = () => {
  const navigate = useNavigate();

  const logoutUser = async () => {
    try {
      // Optional: Call backend to invalidate session/token
      await fetch(API_ENDPOINTS.AUTH.LOGOUT, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          // Include the token in the Authorization header if needed
          Authorization: `Bearer ${localStorage.getItem("token")}`,
        },
        credentials: "include",
      });

      // Clear all authentication-related data from localStorage
      localStorage.removeItem("token");
      localStorage.removeItem("user");

      toast.success("Logged out successfully");
      navigate("/login");
    } catch (err) {
      console.error("Logout error:", err);
      // Even if the server call fails, we should still clear local storage
      // and redirect the user to login
      localStorage.removeItem("token");
      localStorage.removeItem("user");

      toast.success("Logged out successfully");
      navigate("/login");
    }
  };

  return logoutUser;
};

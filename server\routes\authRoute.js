const express = require("express");
const router = express.Router();
const {
  registerUser,
  resendVerificationEmail,
  verifyEmail,
  loginUser,
  forgotPassword,
  resetPassword,
  logoutUser,
  getCurrentUser,
} = require("../controllers/authController");
const {
  loginLimiter,
  forgotPasswordLimiter,
} = require("../middlewares/rateLimiter");
const { verifyToken } = require("../middlewares/verifyToken");

router.post("/register", registerUser);
router.post("/resend-verification", resendVerificationEmail);
router.get("/verify-email/:userId/:token", verifyEmail);
router.post("/login", loginLimiter, loginUser);
router.post("/forgot-password", forgotPasswordLimiter, forgotPassword);
router.post("/reset-password/:userId/:token", resetPassword);
router.post("/logout", logoutUser);
router.get("/me", verifyToken, getCurrentUser);

module.exports = router;

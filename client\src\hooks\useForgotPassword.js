import { useState } from "react";
import { apiClient } from "@/config/api";

export const useForgotPassword = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);

  const handleForgotPassword = async (formData) => {
    try {
      setLoading(true);
      setError(null);
      setSuccess(false);

      const response = await apiClient.post("/auth/forgot-password", formData);

      if (response.data?.success) {
        setSuccess(true);
        return response.data;
      } else {
        setError(response.data?.message || "Failed to send reset email");
        throw new Error(response.data?.message || "Failed to send reset email");
      }
    } catch (err) {
      const errorMessage =
        err.response?.data?.message || err.message || "Something went wrong";
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return { handleForgotPassword, loading, error, success };
};

import { useState } from "react";
import { apiClient } from "@/config/api";

export const useRegister = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);

  const handleRegister = async (formData) => {
    try {
      setLoading(true);
      setError(null);
      setSuccess(false);

      const response = await apiClient.post("/auth/register", formData);

      if (response.data?.success) {
        setSuccess(true);
        return response.data;
      } else {
        setError(response.data?.message || "Registration failed");
        throw new Error(response.data?.message || "Registration failed");
      }
    } catch (err) {
      const errorMessage =
        err.response?.data?.message || err.message || "Something went wrong";
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return { handleRegister, loading, error, success };
};

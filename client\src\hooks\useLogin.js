import { useState } from "react";
import { apiClient } from "@/config/api";

export const useLogin = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);

  const handleLogin = async (formData) => {
    try {
      setLoading(true);
      setError(null);
      setSuccess(false);

      const response = await apiClient.post("/auth/login", formData);

      if (response.data?.success) {
        setSuccess(true);
        // Store token if provided (fallback if not in headers)
        if (response.data.token) {
          localStorage.setItem("token", response.data.token);
        }
        // Store user data for dashboard
        if (response.data.user) {
          localStorage.setItem("user", JSON.stringify(response.data.user));
        }
        return response.data;
      } else {
        setError(response.data?.message || "Lo<PERSON> failed");
        throw new Error(response.data?.message || "<PERSON><PERSON> failed");
      }
    } catch (err) {
      const errorMessage =
        err.response?.data?.message || err.message || "Something went wrong";
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return { handleLogin, loading, error, success };
};

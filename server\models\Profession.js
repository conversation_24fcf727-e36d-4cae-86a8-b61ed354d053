const mongoose = require("mongoose");

const itemSchema = new mongoose.Schema({
  name: { type: String, required: true },
  addedBy: { type: mongoose.Schema.Types.ObjectId, ref: "Admin", required: true },
  updatedBy: { type: mongoose.Schema.Types.ObjectId, ref: "Admin" },
}, { timestamps: true }); // adds createdAt, updatedAt

const subcategorySchema = new mongoose.Schema({
  title: { type: String, required: true },
  items: [itemSchema],
  addedBy: { type: mongoose.Schema.Types.ObjectId, ref: "Admin", required: true },
  updatedBy: { type: mongoose.Schema.Types.ObjectId, ref: "Admin" },
}, { timestamps: true }); // adds createdAt, updatedAt

const professionSchema = new mongoose.Schema({
  professionTitle: { type: String, required: true, unique: true },
  subcategories: [subcategorySchema],
  addedBy: { type: mongoose.Schema.Types.ObjectId, ref: "Admin", required: true },
}, { timestamps: true }); // adds createdAt, updatedAt

const Profession = mongoose.model("Profession", professionSchema);

module.exports = { Profession };

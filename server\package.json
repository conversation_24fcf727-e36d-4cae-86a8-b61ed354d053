{"name": "server", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node index.js", "dev": "nodemon index.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"axios": "^1.11.0", "bcrypt": "^6.0.0", "bcryptjs": "^3.0.2", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^17.2.0", "express": "^5.1.0", "express-rate-limit": "^8.0.1", "jsonwebtoken": "^9.0.2", "mongoose": "^8.16.4", "nodemailer": "^7.0.5", "nodemon": "^3.1.10"}}
import * as React from "react";
import { Filter, RotateCcw, Search } from "lucide-react";

import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Checkbox } from "@/components/ui/checkbox";
import { Slider } from "@/components/ui/slider";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";

// Filter data
const filterData = {
  categories: [
    {
      id: "design",
      name: "Design & Creative",
      subcategories: [
        "Logo Design",
        "Web Design",
        "Graphic Design",
        "UI/UX Design",
        "Illustration",
      ],
    },
    {
      id: "writing",
      name: "Writing & Translation",
      subcategories: [
        "Content Writing",
        "Copywriting",
        "Technical Writing",
        "Translation",
        "Proofreading",
      ],
    },
    {
      id: "programming",
      name: "Programming & Tech",
      subcategories: [
        "Web Development",
        "Mobile Apps",
        "Desktop Apps",
        "DevOps",
        "Data Science",
      ],
    },
    {
      id: "marketing",
      name: "Digital Marketing",
      subcategories: [
        "SEO",
        "Social Media",
        "Content Marketing",
        "Email Marketing",
        "PPC Advertising",
      ],
    },
    {
      id: "video",
      name: "Video & Animation",
      subcategories: [
        "Video Editing",
        "Animation",
        "Motion Graphics",
        "Whiteboard Animation",
        "Video Production",
      ],
    },
  ],
  countries: [
    "India",
    "United States",
    "United Kingdom",
    "Canada",
    "Australia",
    "Germany",
    "France",
    "Brazil",
  ],
  languages: [
    "English",
    "Hindi",
    "Spanish",
    "French",
    "German",
    "Portuguese",
    "Chinese",
    "Arabic",
  ],
};

export function AppSidebar({ ...props }) {
  const [filters, setFilters] = React.useState({
    selectedCategory: "",
    selectedSubcategories: [],
    priceRange: [0, 50000],
    deliveryTime: [],
    sellerLevel: [],
    sellerCountry: "",
    rating: "",
    languages: [],
    ordersInQueue: [],
    onlineNow: false,
  });

  const [searchCountry, setSearchCountry] = React.useState("");
  const [searchLanguage, setSearchLanguage] = React.useState("");

  const handleFilterChange = (key, value) => {
    setFilters((prev) => ({ ...prev, [key]: value }));
  };

  const handleArrayFilterToggle = (key, value) => {
    setFilters((prev) => ({
      ...prev,
      [key]: prev[key].includes(value)
        ? prev[key].filter((item) => item !== value)
        : [...prev[key], value],
    }));
  };

  const resetFilters = () => {
    setFilters({
      selectedCategory: "",
      selectedSubcategories: [],
      priceRange: [0, 50000],
      deliveryTime: [],
      sellerLevel: [],
      sellerCountry: "",
      rating: "",
      languages: [],
      ordersInQueue: [],
      onlineNow: false,
    });
    setSearchCountry("");
    setSearchLanguage("");
  };

  const applyFilters = () => {
    console.log("Applied filters:", filters);
    // Here you would typically call an API or update the parent component
  };

  const filteredCountries = filterData.countries.filter((country) =>
    country.toLowerCase().includes(searchCountry.toLowerCase())
  );

  const filteredLanguages = filterData.languages.filter((language) =>
    language.toLowerCase().includes(searchLanguage.toLowerCase())
  );

  const selectedCategory = filterData.categories.find(
    (cat) => cat.id === filters.selectedCategory
  );

  return (
    <Sidebar variant="floating" className="" {...props}>
      <SidebarHeader className="border-b">
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton size="lg" className="w-full justify-start">
              <div className="bg-primary text-primary-foreground flex aspect-square size-8 items-center justify-center rounded-lg">
                <Filter className="size-4" />
              </div>
              <div className="flex flex-col gap-0.5 leading-none">
                <span className="font-semibold">Filter Gigs</span>
                <span className="text-xs text-muted-foreground">
                  Find your perfect service
                </span>
              </div>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>

      <SidebarContent className="px-4 py-4">
        <div className="space-y-4">
          <Accordion
            type="multiple"
            defaultValue={["category", "price", "delivery"]}
            className="w-full"
          >
            {/* Category Filter */}
            <AccordionItem value="category">
              <AccordionTrigger className="text-sm font-medium">
                Category & Service Type
              </AccordionTrigger>
              <AccordionContent className="space-y-3">
                <div className="space-y-2">
                  <Label className="text-xs font-medium text-muted-foreground">
                    CATEGORY
                  </Label>
                  <Select
                    value={filters.selectedCategory}
                    onValueChange={(value) => {
                      handleFilterChange("selectedCategory", value);
                      handleFilterChange("selectedSubcategories", []);
                    }}
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      {filterData.categories.map((category) => (
                        <SelectItem key={category.id} value={category.id}>
                          {category.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {selectedCategory && (
                  <div className="space-y-2">
                    <Label className="text-xs font-medium text-muted-foreground">
                      SUBCATEGORY
                    </Label>
                    <div className="space-y-2 max-h-32 overflow-y-auto">
                      {selectedCategory.subcategories.map((subcategory) => (
                        <div
                          key={subcategory}
                          className="flex items-center space-x-2"
                        >
                          <Checkbox
                            id={subcategory}
                            checked={filters.selectedSubcategories.includes(
                              subcategory
                            )}
                            onCheckedChange={() =>
                              handleArrayFilterToggle(
                                "selectedSubcategories",
                                subcategory
                              )
                            }
                          />
                          <Label
                            htmlFor={subcategory}
                            className="text-sm font-normal cursor-pointer"
                          >
                            {subcategory}
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </AccordionContent>
            </AccordionItem>

            {/* Price Range Filter */}
            <AccordionItem value="price">
              <AccordionTrigger className="text-sm font-medium">
                Price Range
              </AccordionTrigger>
              <AccordionContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex items-center justify-between text-sm">
                    <span>₹{filters.priceRange[0].toLocaleString()}</span>
                    <span>₹{filters.priceRange[1].toLocaleString()}</span>
                  </div>
                  <Slider
                    value={filters.priceRange}
                    onValueChange={(value) =>
                      handleFilterChange("priceRange", value)
                    }
                    max={50000}
                    min={0}
                    step={100}
                    className="w-full"
                  />
                  <div className="grid grid-cols-2 gap-2">
                    <div className="space-y-1">
                      <Label className="text-xs">Min Price</Label>
                      <Input
                        type="number"
                        value={filters.priceRange[0]}
                        onChange={(e) => {
                          const value = parseInt(e.target.value) || 0;
                          handleFilterChange("priceRange", [
                            value,
                            filters.priceRange[1],
                          ]);
                        }}
                        className="h-8"
                      />
                    </div>
                    <div className="space-y-1">
                      <Label className="text-xs">Max Price</Label>
                      <Input
                        type="number"
                        value={filters.priceRange[1]}
                        onChange={(e) => {
                          const value = parseInt(e.target.value) || 50000;
                          handleFilterChange("priceRange", [
                            filters.priceRange[0],
                            value,
                          ]);
                        }}
                        className="h-8"
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label className="text-xs font-medium text-muted-foreground">
                      QUICK PRESETS
                    </Label>
                    <div className="grid grid-cols-2 gap-2">
                      {[
                        [0, 500],
                        [500, 1000],
                        [1000, 5000],
                        [5000, 50000],
                      ].map(([min, max]) => (
                        <Button
                          key={`${min}-${max}`}
                          variant="outline"
                          size="sm"
                          className="h-7 text-xs"
                          onClick={() =>
                            handleFilterChange("priceRange", [min, max])
                          }
                        >
                          ₹{min.toLocaleString()}-₹{max.toLocaleString()}
                        </Button>
                      ))}
                    </div>
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* Delivery Time Filter */}
            <AccordionItem value="delivery">
              <AccordionTrigger className="text-sm font-medium">
                Delivery Time
              </AccordionTrigger>
              <AccordionContent className="space-y-3">
                <div className="space-y-2">
                  {[
                    "Within 24 Hours",
                    "1-3 Days",
                    "3-7 Days",
                    "1-2 Weeks",
                    "2+ Weeks",
                  ].map((time) => (
                    <div key={time} className="flex items-center space-x-2">
                      <Checkbox
                        id={time}
                        checked={filters.deliveryTime.includes(time)}
                        onCheckedChange={() =>
                          handleArrayFilterToggle("deliveryTime", time)
                        }
                      />
                      <Label
                        htmlFor={time}
                        className="text-sm font-normal cursor-pointer"
                      >
                        {time}
                      </Label>
                    </div>
                  ))}
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* Seller Level Filter */}
            <AccordionItem value="seller-level">
              <AccordionTrigger className="text-sm font-medium">
                Seller Level
              </AccordionTrigger>
              <AccordionContent className="space-y-3">
                <div className="space-y-2">
                  {["New Seller", "Level One", "Level Two", "Top Rated"].map(
                    (level) => (
                      <div key={level} className="flex items-center space-x-2">
                        <Checkbox
                          id={level}
                          checked={filters.sellerLevel.includes(level)}
                          onCheckedChange={() =>
                            handleArrayFilterToggle("sellerLevel", level)
                          }
                        />
                        <Label
                          htmlFor={level}
                          className="text-sm font-normal cursor-pointer"
                        >
                          {level}
                        </Label>
                      </div>
                    )
                  )}
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* Seller Country Filter */}
            <AccordionItem value="country">
              <AccordionTrigger className="text-sm font-medium">
                Seller Country
              </AccordionTrigger>
              <AccordionContent className="space-y-3">
                <div className="space-y-2">
                  <div className="relative">
                    <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search countries..."
                      value={searchCountry}
                      onChange={(e) => setSearchCountry(e.target.value)}
                      className="pl-8 h-9"
                    />
                  </div>
                  <Select
                    value={filters.sellerCountry}
                    onValueChange={(value) =>
                      handleFilterChange("sellerCountry", value)
                    }
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select country" />
                    </SelectTrigger>
                    <SelectContent>
                      {filteredCountries.map((country) => (
                        <SelectItem key={country} value={country}>
                          {country}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* Rating Filter */}
            <AccordionItem value="rating">
              <AccordionTrigger className="text-sm font-medium">
                Rating
              </AccordionTrigger>
              <AccordionContent className="space-y-3">
                <RadioGroup
                  value={filters.rating}
                  onValueChange={(value) => handleFilterChange("rating", value)}
                  className="space-y-2"
                >
                  {[
                    { value: "5", label: "5 stars only" },
                    { value: "4+", label: "4+ stars" },
                    { value: "3+", label: "3+ stars" },
                    { value: "any", label: "Any rating" },
                  ].map((rating) => (
                    <div
                      key={rating.value}
                      className="flex items-center space-x-2"
                    >
                      <RadioGroupItem value={rating.value} id={rating.value} />
                      <Label
                        htmlFor={rating.value}
                        className="text-sm font-normal cursor-pointer"
                      >
                        {rating.label}
                      </Label>
                    </div>
                  ))}
                </RadioGroup>
              </AccordionContent>
            </AccordionItem>

            {/* Language Filter */}
            <AccordionItem value="language">
              <AccordionTrigger className="text-sm font-medium">
                Language
              </AccordionTrigger>
              <AccordionContent className="space-y-3">
                <div className="space-y-2">
                  <div className="relative">
                    <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search languages..."
                      value={searchLanguage}
                      onChange={(e) => setSearchLanguage(e.target.value)}
                      className="pl-8 h-9"
                    />
                  </div>
                  <div className="space-y-2 max-h-32 overflow-y-auto">
                    {filteredLanguages.map((language) => (
                      <div
                        key={language}
                        className="flex items-center space-x-2"
                      >
                        <Checkbox
                          id={language}
                          checked={filters.languages.includes(language)}
                          onCheckedChange={() =>
                            handleArrayFilterToggle("languages", language)
                          }
                        />
                        <Label
                          htmlFor={language}
                          className="text-sm font-normal cursor-pointer"
                        >
                          {language}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* Orders in Queue Filter */}
            <AccordionItem value="orders">
              <AccordionTrigger className="text-sm font-medium">
                Orders in Queue
              </AccordionTrigger>
              <AccordionContent className="space-y-3">
                <div className="space-y-2">
                  {["0-5", "5-10", "10+"].map((range) => (
                    <div key={range} className="flex items-center space-x-2">
                      <Checkbox
                        id={range}
                        checked={filters.ordersInQueue.includes(range)}
                        onCheckedChange={() =>
                          handleArrayFilterToggle("ordersInQueue", range)
                        }
                      />
                      <Label
                        htmlFor={range}
                        className="text-sm font-normal cursor-pointer"
                      >
                        {range} orders
                      </Label>
                    </div>
                  ))}
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* Online Now Filter */}
            <AccordionItem value="online">
              <AccordionTrigger className="text-sm font-medium">
                Availability
              </AccordionTrigger>
              <AccordionContent className="space-y-3">
                <div className="flex items-center justify-between">
                  <Label htmlFor="online-now" className="text-sm font-normal">
                    Online Now
                  </Label>
                  <Switch
                    id="online-now"
                    checked={filters.onlineNow}
                    onCheckedChange={(checked) =>
                      handleFilterChange("onlineNow", checked)
                    }
                  />
                </div>
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </div>
      </SidebarContent>

      {/* Filter Actions */}
      <div className="border-t p-4 space-y-2">
        <Button onClick={applyFilters} className="w-full" size="sm">
          Apply Filters
        </Button>
        <Button
          onClick={resetFilters}
          variant="outline"
          className="w-full"
          size="sm"
        >
          <RotateCcw className="w-4 h-4 mr-2" />
          Reset Filters
        </Button>
      </div>
    </Sidebar>
  );
}

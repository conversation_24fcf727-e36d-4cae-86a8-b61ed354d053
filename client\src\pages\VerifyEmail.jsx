import { useEffect, useState, useCallback } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { toast } from "sonner";
import { LoaderCircle, CheckCircle, XCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { apiClient } from "@/config/api";

const VerifyEmail = () => {
  const { userId, token } = useParams();
  const navigate = useNavigate();
  const [status, setStatus] = useState("verifying"); // 'verifying', 'success', 'failed'

  // State for resend functionality
  const [isResending, setIsResending] = useState(false);
  const [resendTimer, setResendTimer] = useState(0);
  const [canResend, setCanResend] = useState(true);

  // Function to verify email
  const verifyEmail = useCallback(async () => {
    if (!userId || !token) {
      setStatus("failed");
      toast.error("Invalid verification link.");
      return;
    }
    setStatus("verifying");
    try {
      const res = await apiClient.get(`/auth/verify-email/${userId}/${token}`);
      if (res.data.success) {
        setStatus("success");
        toast.success("Email verified successfully!");
      } else {
        setStatus("failed");
        toast.error(res.data.message || "Verification failed");
      }
    } catch (error) {
      setStatus("failed");
      toast.error(
        error?.response?.data?.message ||
          "Something went wrong while verifying."
      );
    }
  }, [userId, token]);

  useEffect(() => {
    verifyEmail();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [verifyEmail]);

  // Timer countdown for resend button
  useEffect(() => {
    if (resendTimer > 0) {
      const timer = setTimeout(() => {
        setResendTimer(resendTimer - 1);
      }, 1000);
      return () => clearTimeout(timer);
    } else if (resendTimer === 0 && !canResend) {
      setCanResend(true);
    }
  }, [resendTimer, canResend]);

  // Handler for navigating to login
  const handleGoToLogin = () => {
    navigate("/login");
  };

  // Handler for requesting a new verification link
  const handleResendEmail = async (e) => {
    e?.preventDefault();
    if (!userId) {
      toast.error("User information not found. Please try registering again.");
      navigate("/register");
      return;
    }
    if (!canResend || resendTimer > 0) return;

    setIsResending(true);
    try {
      await apiClient.post("/auth/resend-verification", {
        userId,
      });
      toast.success("Verification email sent again!");
      setResendTimer(60); // 60 seconds cooldown
      setCanResend(false);
    } catch (error) {
      console.error(error);
      const message =
        error?.response?.data?.message ||
        "Failed to resend verification email. Please try again.";
      toast.error(message);
    } finally {
      setIsResending(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-background px-4">
      <Card className="w-full max-w-md">
        <CardContent className="py-10 space-y-6 text-center">
          {/* Verifying State */}
          {status === "verifying" && (
            <>
              <div className="flex justify-center">
                <LoaderCircle className="h-8 w-8 animate-spin text-muted-foreground" />
              </div>
              <p className="text-sm text-muted-foreground">
                Verifying your email...
              </p>
            </>
          )}

          {/* Success State */}
          {status === "success" && (
            <>
              <div className="flex justify-center">
                <CheckCircle className="h-8 w-8 text-green-600" />
              </div>
              <h1 className="text-xl font-semibold text-foreground">
                Email Verified
              </h1>
              <p className="text-muted-foreground text-sm">
                Your account has been successfully verified. You can now log in.
              </p>
              <Button onClick={handleGoToLogin} className="w-full">
                Go to Login
              </Button>
            </>
          )}

          {/* Failed State */}
          {status === "failed" && (
            <>
              <div className="flex justify-center">
                <XCircle className="h-8 w-8 text-red-600" />
              </div>
              <h1 className="text-xl font-semibold text-foreground">
                Verification Failed
              </h1>
              <p className="text-muted-foreground text-sm">
                The verification link is invalid or has expired.
              </p>
              <Button
                variant="outline"
                className="w-full"
                onClick={handleResendEmail}
                disabled={!canResend || resendTimer > 0 || isResending}
              >
                {isResending
                  ? "Sending..."
                  : `Get New Link${
                      resendTimer > 0 ? ` (${resendTimer}s)` : ""
                    }`}
              </Button>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default VerifyEmail;
